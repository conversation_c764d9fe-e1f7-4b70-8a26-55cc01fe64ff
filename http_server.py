from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uvicorn
from BASE.http.chat import chat_stream
from  BASE.http.watchdog import watchdog_file_delete, watchdog_file_update
from BASE.http.kb.delete_kb import delete_kb_source
from BASE.http.kb.list_kbs import list_knowledge_bases
# from BASE.http.swagger import swagger_list
from BASE.http.auto_actions import auto_actions
import json
from BASE.http.swagger import swagger_stream
from BASE.http.codelens import debug_code, optimize_code, review_code, test_code


app = FastAPI(title="CodeMate HTTP API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.post("/chat/stream")
async def chat_stream_handler(request: Request):
    print("Received chat request")
    data = await request.json()

    print(type(data)) 

    print(f"complete chat data : {json.dumps(data, indent=2)}") 


    # Handle both 'model' and 'provider' fields for backward compatibility
    model = data.get("model") or data.get("provider", "bodh-x1")
    if model == "default":
        model = "gpt-4.1-mini"

    llm_ = {
        "base_url": data.get("base_url", "https://backend.v3.codemateai.dev/v1"),
        "api_key": data.get("api_key", "sk-DS1Y-HT1DnXEhLyKckXQ6A"),
        "model": model,
    }

    # Extract additional parameters
    session_id = request.headers.get("x-session", "")

    return StreamingResponse(
        chat_stream(
            llm_=llm_,
            messages=data["messages"],
            call_for="chat",
            session_id=session_id,
            is_web_search=data["web_search"]
        ),
        media_type="text/event-stream"
    )


@app.post("/swagger/stream")
async def swagger_stream_handler(request: Request):
    data = await request.json()

    # Handle both 'model' and 'provider' fields for backward compatibility
    model = data.get("model") or data.get("provider", "bodh-x1")
    if model == "default":
        model = "gpt-4.1-mini"

    llm_config = {
        "base_url": data.get("base_url", "https://backend.v3.codemateai.dev/v1"),
        "api_key": data.get("api_key", "sk-DS1Y-HT1DnXEhLyKckXQ6A"),
        "model": model,
    }

    # Add llm_config to the data being passed to swagger_stream
    swagger_data = data.get("data", {})
    swagger_data["llm_config"] = llm_config

    return StreamingResponse(
        swagger_stream(
            swagger_data,
            session_id=request.headers.get("x-session", "")
        ),
        media_type="text/event-stream"
    )

@app.post("/delete/kb")
async def delete_kb_handler(request: Request):
    data = await request.json()
    kbid = data.get("kbid", "")
    source = data.get("source", "both")

    return JSONResponse(delete_kb_source(kbid, source=source))



@app.get("/list_kbs")
async def list_kb(request: Request):
    include_cloud = request.query_params.get("include_cloud", "false").lower() == "true"
    session = request.headers.get("x-session")

    print(f"include_cloud: {include_cloud}, session: {session}")

    result = await list_knowledge_bases(include_cloud=include_cloud, session=session)
    return JSONResponse(result)



# @app.post("/swagger/list")
# async def get_swagger_list(request: Request):
#     data = await request.json()
#     return JSONResponse(await swagger_list(data))


@app.post("/watchdog/file_delete")
async def watchdog_file_delete_handler(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    return JSONResponse(watchdog_file_delete(file_path=file_path, call_for="delete"))


@app.post("/watchdog/file_update")
async def watchdog_file_update_handler(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    return JSONResponse(watchdog_file_update(file_path=file_path, call_for="update"))


@app.post("/auto-actions/{operation}")
async def auto_actions_handler(request: Request, operation: str):
    data = await request.json()
    session_id = request.headers.get("x-session")
    return JSONResponse(auto_actions(data, session_id=session_id, operation=operation))



@app.post("/debug/code")
async def debug_code_handler(request: Request):
    data = await request.json()
    session_id = request.headers.get("x-session")
    # print(f"Received debug code request: {data}")
    return JSONResponse(debug_code(data, session_id=session_id))


@app.post('/test/code')
async def test_code_handler(request: Request):
    data = await request.json()
    session_id = request.headers.get("x-session")
    return JSONResponse(test_code(data, session_id=session_id))


@app.post('/review/code')
async def review_code_handler(request: Request):
    data = await request.json()
    session_id = request.headers.get("x-session")
    return JSONResponse(review_code(data, session_id=session_id))

@app.post('/optimize/code')
async def optimize_code_handler(request: Request):
    data = await request.json()
    session_id = request.headers.get("x-session")
    return JSONResponse(optimize_code(data, session_id=session_id))

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=45213)